using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// SQLite-based implementation of stock bar caching service.
/// Provides efficient caching of Alpaca/Polygon stock data to minimize API calls.
/// Implements 1-year retention with differential updates.
/// </summary>
public sealed class StockBarCacheService : IStockBarCacheService
{
    private readonly StockBarCacheDbContext _dbContext;
    private readonly ILogger<StockBarCacheService> _logger;

    public StockBarCacheService(StockBarCacheDbContext dbContext, ILogger<StockBarCacheService> logger)
    {
        _dbContext = dbContext;
        _logger = logger;
    }

    public async Task<IReadOnlyList<IBar>> GetCachedBarsAsync(string symbol, string timeFrame, DateTime startDate, DateTime endDate)
    {
        try
        {
            _logger.LogDebug("Retrieving cached bars for {Symbol} {TimeFrame} from {StartDate} to {EndDate}", 
                symbol, timeFrame, startDate.ToString("yyyy-MM-dd"), endDate.ToString("yyyy-MM-dd"));

            var cachedBars = await _dbContext.GetCachedBarsAsync(symbol, timeFrame, startDate, endDate);
            var bars = cachedBars.Select(cb => cb.ToIBar()).Cast<IBar>().ToList();

            _logger.LogDebug("Found {Count} cached bars for {Symbol} {TimeFrame}", bars.Count, symbol, timeFrame);
            return bars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached bars for {Symbol} {TimeFrame}", symbol, timeFrame);
            return new List<IBar>();
        }
    }

    public async Task CacheBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars)
    {
        try
        {
            var barsList = bars.ToList();
            if (!barsList.Any())
            {
                _logger.LogDebug("No bars to cache for {Symbol} {TimeFrame}", symbol, timeFrame);
                return;
            }

            _logger.LogDebug("Caching {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);

            await _dbContext.AddOrUpdateCachedBarsAsync(symbol, timeFrame, barsList);

            _logger.LogInformation("Successfully cached {Count} bars for {Symbol} {TimeFrame}", barsList.Count, symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching bars for {Symbol} {TimeFrame}", symbol, timeFrame);
            throw;
        }
    }

    public async Task<(DateTime startDate, DateTime endDate)?> GetMissingDateRangeAsync(string symbol, string timeFrame, DateTime requestedStartDate, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol, timeFrame);
            var earliestCachedDate = await GetEarliestCachedDateAsync(symbol, timeFrame);

            // If no cached data, fetch entire range
            if (!latestCachedDate.HasValue || !earliestCachedDate.HasValue)
            {
                _logger.LogDebug("No cached data for {Symbol} {TimeFrame}, need to fetch entire range", symbol, timeFrame);
                return (requestedStartDate, requestedEndDate);
            }

            // If cache covers the entire requested range, no fetch needed
            if (earliestCachedDate.Value <= requestedStartDate && latestCachedDate.Value >= requestedEndDate)
            {
                _logger.LogDebug("Cache for {Symbol} {TimeFrame} covers entire requested range", symbol, timeFrame);
                return null;
            }

            // FIXED: Determine what range needs to be fetched with proper logic
            // We need to identify gaps and fetch only what's missing

            // Case 1: Need newer data (requested end is after latest cached)
            if (requestedEndDate > latestCachedDate.Value)
            {
                var fetchStartDate = Math.Max(requestedStartDate.Ticks, latestCachedDate.Value.AddDays(1).Ticks);
                var fetchStart = new DateTime(fetchStartDate);

                _logger.LogDebug("Need newer data for {Symbol} {TimeFrame}: fetch {StartDate} to {EndDate}",
                    symbol, timeFrame, fetchStart.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));

                return (fetchStart, requestedEndDate);
            }

            // Case 2: Need older data (requested start is before earliest cached)
            if (requestedStartDate < earliestCachedDate.Value)
            {
                var fetchEndDate = Math.Min(requestedEndDate.Ticks, earliestCachedDate.Value.AddDays(-1).Ticks);
                var fetchEnd = new DateTime(fetchEndDate);

                // Ensure we have a valid range
                if (requestedStartDate >= fetchEnd)
                {
                    _logger.LogDebug("No gap to fill for older data for {Symbol} {TimeFrame}", symbol, timeFrame);
                    return null;
                }

                _logger.LogDebug("Need older data for {Symbol} {TimeFrame}: fetch {StartDate} to {EndDate}",
                    symbol, timeFrame, requestedStartDate.ToString("yyyy-MM-dd"), fetchEnd.ToString("yyyy-MM-dd"));

                return (requestedStartDate, fetchEnd);
            }

            // Case 3: Requested range is within cached range but may have gaps
            // For now, assume cache is complete within its range
            _logger.LogDebug("Requested range for {Symbol} {TimeFrame} is within cached range, no fetch needed", symbol, timeFrame);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining missing date range for {Symbol} {TimeFrame}", symbol, timeFrame);
            // On error, fetch entire range to be safe
            return (requestedStartDate, requestedEndDate);
        }
    }

    public async Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            return await _dbContext.GetLatestCachedDateAsync(symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting latest cached date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame)
    {
        try
        {
            return await _dbContext.GetEarliestCachedDateAsync(symbol, timeFrame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting earliest cached date for {Symbol} {TimeFrame}", symbol, timeFrame);
            return null;
        }
    }

    public async Task<bool> IsCacheFreshAsync(string symbol, string timeFrame, DateTime requestedEndDate)
    {
        try
        {
            var latestCachedDate = await GetLatestCachedDateAsync(symbol, timeFrame);

            if (!latestCachedDate.HasValue)
                return false;

            // First check: Does cache have data up to the requested end date
            var hasRequiredData = latestCachedDate.Value >= requestedEndDate;

            if (!hasRequiredData)
            {
                _logger.LogDebug("Cache missing required data for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}",
                    symbol, timeFrame, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"));
                return false;
            }

            // Second check: Is the latest cached data within staleness threshold
            // Use the latest cached date as the data timestamp for staleness validation
            var dataAge = DateTime.UtcNow - latestCachedDate.Value;
            var isMarketHours = IsMarketHours();

            // Apply market-hours-aware staleness threshold
            var stalenessThreshold = isMarketHours
                ? TimeSpan.FromMinutes(18)  // Market hours: 18 minutes
                : TimeSpan.FromHours(8);    // After hours: 8 hours

            var isDataFresh = dataAge <= stalenessThreshold;

            _logger.LogDebug("Cache freshness for {Symbol} {TimeFrame}: latest={LatestDate}, requested={RequestedDate}, " +
                           "dataAge={DataAge}, threshold={Threshold}, marketHours={IsMarketHours}, fresh={IsFresh}",
                symbol, timeFrame, latestCachedDate.Value.ToString("yyyy-MM-dd"), requestedEndDate.ToString("yyyy-MM-dd"),
                dataAge, stalenessThreshold, isMarketHours, isDataFresh);

            return isDataFresh;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache freshness for {Symbol} {TimeFrame}", symbol, timeFrame);
            return false;
        }
    }

    /// <summary>
    /// Determines if the current time is during market hours (9:30 AM - 4:00 PM ET, Monday-Friday).
    /// </summary>
    private bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }

    public async Task PerformMaintenanceAsync(int retainDays = 365)
    {
        try
        {
            _logger.LogInformation("Starting stock cache maintenance, retaining {RetainDays} days of data", retainDays);

            await _dbContext.CleanupOldDataAsync(retainDays);

            _logger.LogInformation("Stock cache maintenance completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during stock cache maintenance");
            throw;
        }
    }

    public async Task InitializeCacheAsync()
    {
        try
        {
            _logger.LogDebug("Initializing stock cache database");

            await _dbContext.EnsureDatabaseCreatedAsync();

            _logger.LogDebug("Stock cache database initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing stock cache database");
            throw;
        }
    }

    public async Task<IDictionary<string, CacheStats>> GetCacheStatsAsync()
    {
        try
        {
            return await _dbContext.GetCacheStatsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return new Dictionary<string, CacheStats>();
        }
    }
}
