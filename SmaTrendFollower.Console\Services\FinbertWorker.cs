using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Net.Http.Json;
using System.Threading.Channels;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that processes news articles from channel and calls FinBERT for sentiment analysis.
/// Implements parallel processing with semaphore rate limiting and stores results in Redis.
/// </summary>
internal sealed class FinbertWorker : BackgroundService
{
    private readonly ChannelReader<HeadlineItem> _reader;
    private readonly IHttpClientFactory _http;
    private readonly IDatabase _redis;
    private readonly ILogger<FinbertWorker> _log;
    private readonly FinbertOptions _opt;
    private readonly LlmSentimentOptions _llmOpt;
    private readonly LlmSentimentService _llmSvc;
    private const string LatestField = "latest";

    /// <summary>
    /// Initializes the FinBERT worker with channel reader and dependencies.
    /// </summary>
    /// <param name="channel">Channel for receiving news articles from polling service</param>
    /// <param name="http">HTTP client factory for FinBERT API calls</param>
    /// <param name="redisSvc">Redis connection service for storing sentiment scores</param>
    /// <param name="opt">FinBERT configuration options</param>
    /// <param name="llmSvc">LLM sentiment service for overlay analysis</param>
    /// <param name="llmOpt">LLM sentiment configuration options</param>
    /// <param name="log">Logger for worker operations</param>
    public FinbertWorker(
        Channel<HeadlineItem> channel,
        IHttpClientFactory http,
        IOptimizedRedisConnectionService redisSvc,
        IOptions<FinbertOptions> opt,
        LlmSentimentService llmSvc,
        IOptions<LlmSentimentOptions> llmOpt,
        ILogger<FinbertWorker> log)
    {
        _reader = channel.Reader ?? throw new ArgumentNullException(nameof(channel));
        _http = http ?? throw new ArgumentNullException(nameof(http));
        _redis = redisSvc.GetDatabaseAsync().GetAwaiter().GetResult();
        _opt = opt.Value ?? throw new ArgumentNullException(nameof(opt));
        _llmSvc = llmSvc ?? throw new ArgumentNullException(nameof(llmSvc));
        _llmOpt = llmOpt.Value ?? throw new ArgumentNullException(nameof(llmOpt));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// Continuously processes news articles from channel with parallel FinBERT calls.
    /// Uses semaphore to limit concurrent HTTP requests to FinBERT endpoint.
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken ct)
    {
        _log.LogInformation("FinbertWorker started with parallelism: {Parallelism}", _opt.Parallelism);
        
        var sem = new SemaphoreSlim(_opt.Parallelism);
        
        try
        {
            await foreach (var article in _reader.ReadAllAsync(ct))
            {
                await sem.WaitAsync(ct);
                
                // Fire-and-forget parallel processing
                _ = Task.Run(async () =>
                {
                    try 
                    { 
                        await ProcessAsync(article, ct); 
                    }
                    catch (Exception ex) 
                    { 
                        _log.LogDebug(ex, "FinBERT processing error for article {Id}", article.Id); 
                    }
                    finally 
                    { 
                        sem.Release(); 
                    }
                }, ct);
            }
        }
        catch (OperationCanceledException)
        {
            _log.LogInformation("FinbertWorker cancelled");
            throw; // Propagate cancellation as expected by BackgroundService
        }
        finally
        {
            sem.Dispose();
            _log.LogInformation("FinbertWorker stopped");
        }
    }

    /// <summary>
    /// Processes a single news article: calls FinBERT API and stores sentiment in Redis.
    /// Handles symbol extraction, sentiment scoring, and Redis storage with TTL.
    /// </summary>
    /// <param name="n">News article to process</param>
    /// <param name="ct">Cancellation token</param>
    private async Task ProcessAsync(HeadlineItem n, CancellationToken ct)
    {
        try
        {
            // Call FinBERT HTTP endpoint
            var res = await _http.CreateClient("Finbert")
                                 .PostAsJsonAsync(_opt.BaseUrl, new { text = n.Headline }, ct);
            res.EnsureSuccessStatusCode();
            
            var json = await res.Content.ReadFromJsonAsync<FinbertResp>(cancellationToken: ct);
            if (json == null)
            {
                _log.LogWarning("FinBERT returned null response for article {Id}", n.Id);
                return;
            }

            // Convert FinBERT response to sentiment score (-1 to +1)
            var score = json.label == "positive" ? json.score
                       : json.label == "negative" ? -json.score
                       : 0.0;

            // ---------- LLM overlay if low-confidence ----------
            if (Math.Abs(score) < _llmOpt.ConfidenceCutoff)
            {
                var llm = await _llmSvc.GetSentimentAsync(n.Headline, ct);
                if (llm is not null)
                {
                    var originalScore = score;
                    score = (1.0 - _llmOpt.BlendWeight) * score
                          + _llmOpt.BlendWeight * llm.Value;

                    _log.LogDebug("LLM overlay for {Symbol}: FinBERT={FinBert:+0.000;-0.000}, LLM={Llm:+0.000;-0.000}, Blended={Final:+0.000;-0.000}",
                        n.Symbol, originalScore, llm.Value, score);
                }
            }

            // Store sentiment for the symbol associated with this headline
            if (string.IsNullOrWhiteSpace(n.Symbol))
            {
                _log.LogDebug("No symbol found for headline {Id}: {Headline}", n.Id, n.Headline);
                return;
            }

            var dateKey = n.CreatedAtUtc.Date.ToString("yyyyMMdd");
            var symbol = n.Symbol;

            var key = $"Sentiment:{symbol}:{dateKey}";
            var field = n.Id; // Use headline ID as field name

            // Store both the specific headline sentiment and update latest
            await _redis.HashSetAsync(key, new[]
            {
                new HashEntry(field, score),
                new HashEntry(LatestField, score)
            });

            // Set TTL for the key
            await _redis.KeyExpireAsync(key, TimeSpan.FromDays(_opt.TtlDays));

            _log.LogDebug("FinBERT {Symbol} {Score:+0.000;-0.000} (ID: {Id})",
                symbol, score, n.Id);
        }
        catch (HttpRequestException ex)
        {
            _log.LogWarning(ex, "FinBERT HTTP error for article {Id}", n.Id);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _log.LogWarning("FinBERT timeout for article {Id}", n.Id);
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Unexpected error processing article {Id}: {Headline}", n.Id, n.Headline);
        }
    }

    /// <summary>
    /// FinBERT API response model matching the expected JSON structure.
    /// </summary>
    /// <param name="label">Sentiment label: "positive", "negative", or "neutral"</param>
    /// <param name="score">Confidence score (0.0 to 1.0)</param>
    private sealed record FinbertResp(string label, double score);
}
