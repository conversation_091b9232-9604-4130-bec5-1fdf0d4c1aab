<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <!-- Suppress NU1608 warning for Pomelo.EntityFrameworkCore.MySql version constraint -->
    <NoWarn>$(NoWarn);NU1608</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EFCore.BulkExtensions" Version="8.1.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.41" />
    <PackageReference Include="Discord.Net" Version="3.15.3" />
    <PackageReference Include="System.Text.Json" Version="9.0.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SmaTrendFollower.Console\SmaTrendFollower.Console.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\SmaTrendFollower.Console\appsettings.json" Link="appsettings.json" CopyToOutputDirectory="PreserveNewest" />
    <None Include="..\SmaTrendFollower.Console\appsettings.Development.json" Link="appsettings.Development.json" CopyToOutputDirectory="PreserveNewest" />
    <None Include="..\SmaTrendFollower.Console\appsettings.LocalProd.json" Link="appsettings.LocalProd.json" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>
