using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NSubstitute;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Tests.Core.Services;

public class LlmSentimentServiceTests
{
    [Fact]
    public void LlmSentimentOptions_DefaultValues_AreCorrect()
    {
        // Arrange & Act
        var options = new LlmSentimentOptions();

        // Assert
        options.Provider.Should().Be("OpenAI");
        options.Model.Should().Be("gpt-4o-mini");
        options.BlendWeight.Should().Be(0.30);
        options.ConfidenceCutoff.Should().Be(0.25);
        options.TimeoutSeconds.Should().Be(10);
    }

    [Fact]
    public void LlmSentimentService_Constructor_RequiresAllParameters()
    {
        // Arrange
        var httpClientFactory = Substitute.For<IHttpClientFactory>();
        var options = Options.Create(new LlmSentimentOptions());
        var logger = Substitute.For<ILogger<LlmSentimentService>>();

        // Act & Assert
        var service = new LlmSentimentService(httpClientFactory, options, logger);
        service.Should().NotBeNull();
    }

    [Fact]
    public async Task GetSentimentAsync_InvalidProvider_ReturnsNull()
    {
        // Arrange
        var httpClientFactory = Substitute.For<IHttpClientFactory>();
        var options = Options.Create(new LlmSentimentOptions { Provider = "InvalidProvider" });
        var logger = Substitute.For<ILogger<LlmSentimentService>>();
        var service = new LlmSentimentService(httpClientFactory, options, logger);

        // Act
        var result = await service.GetSentimentAsync("Some text");

        // Assert
        result.Should().BeNull();
    }

}
