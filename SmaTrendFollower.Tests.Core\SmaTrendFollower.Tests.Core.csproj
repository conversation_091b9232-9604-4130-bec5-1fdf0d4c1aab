﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <!-- Suppress NU1608 warning for Pomelo.EntityFrameworkCore.MySql version constraint -->
    <NoWarn>$(NoWarn);NU1608</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="EFCore.BulkExtensions" Version="8.1.1" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageReference Include="FluentAssertions" Version="6.*" />
    <PackageReference Include="RichardSzalay.MockHttp" Version="7.*" />
    <PackageReference Include="NSubstitute" Version="5.*" />
    <PackageReference Include="StackExchange.Redis" Version="2.*" />
    <PackageReference Include="Microsoft.ML" Version="4.*" />
    <PackageReference Include="Quartz" Version="3.*" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SmaTrendFollower.Console\SmaTrendFollower.Console.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="appsettings.Development.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
