using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using SmaTrendFollower.Configuration;
using System;
using System.Threading.Tasks;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for validating data staleness across the system.
/// Provides centralized staleness checking with market-hours awareness.
/// </summary>
public interface IDataStalenessValidationService
{
    /// <summary>
    /// Validates if data is fresh enough for use
    /// </summary>
    /// <param name="dataTimestamp">Timestamp of the data</param>
    /// <param name="dataType">Type of data being validated</param>
    /// <param name="dataSource">Source of the data (for logging)</param>
    /// <returns>Validation result</returns>
    Task<DataStalenessValidationResult> ValidateDataFreshnessAsync(DateTime dataTimestamp, DataType dataType, string dataSource);
    
    /// <summary>
    /// Gets the current staleness threshold for a data type
    /// </summary>
    /// <param name="dataType">Type of data</param>
    /// <returns>Current staleness threshold</returns>
    TimeSpan GetCurrentThreshold(DataType dataType);
    
    /// <summary>
    /// Checks if it's currently market hours
    /// </summary>
    /// <returns>True if market is open, false otherwise</returns>
    bool IsMarketHours();
}

/// <summary>
/// Implementation of data staleness validation service
/// </summary>
public class DataStalenessValidationService : IDataStalenessValidationService
{
    private readonly DataStalenessConfiguration _config;
    private readonly ILogger<DataStalenessValidationService> _logger;

    public DataStalenessValidationService(
        IOptions<DataStalenessConfiguration> config,
        ILogger<DataStalenessValidationService> logger)
    {
        _config = config.Value;
        _logger = logger;
    }

    public Task<DataStalenessValidationResult> ValidateDataFreshnessAsync(DateTime dataTimestamp, DataType dataType, string dataSource)
    {
        var isMarketHours = IsMarketHours();
        var threshold = _config.GetThreshold(dataType, isMarketHours);
        var dataAge = DateTime.UtcNow - dataTimestamp;
        var isStale = dataAge > threshold;

        var result = new DataStalenessValidationResult
        {
            IsStale = isStale,
            DataAge = dataAge,
            Threshold = threshold,
            DataType = dataType,
            DataSource = dataSource,
            IsMarketHours = isMarketHours,
            ValidationTimestamp = DateTime.UtcNow
        };

        if (isStale)
        {
            if (_config.LogStalenessWarnings)
            {
                _logger.LogWarning(
                    "Stale data detected: {DataType} from {DataSource} is {DataAge} old (threshold: {Threshold}, market hours: {IsMarketHours})",
                    dataType, dataSource, dataAge, threshold, isMarketHours);
            }

            if (_config.RejectStaleData && _config.EnableStrictStalenessChecks)
            {
                result.ShouldReject = true;
                _logger.LogError(
                    "Rejecting stale data: {DataType} from {DataSource} exceeds staleness threshold",
                    dataType, dataSource);
            }
        }
        else
        {
            _logger.LogDebug(
                "Data freshness validated: {DataType} from {DataSource} is {DataAge} old (threshold: {Threshold})",
                dataType, dataSource, dataAge, threshold);
        }

        return Task.FromResult(result);
    }

    public TimeSpan GetCurrentThreshold(DataType dataType)
    {
        var isMarketHours = IsMarketHours();
        return _config.GetThreshold(dataType, isMarketHours);
    }

    public bool IsMarketHours()
    {
        var easternTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow,
            TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));

        // Check if it's a weekday
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        // Check if it's during market hours (9:30 AM - 4:00 PM ET)
        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);

        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }
}

/// <summary>
/// Result of data staleness validation
/// </summary>
public class DataStalenessValidationResult
{
    /// <summary>
    /// Whether the data is considered stale
    /// </summary>
    public bool IsStale { get; set; }
    
    /// <summary>
    /// Age of the data
    /// </summary>
    public TimeSpan DataAge { get; set; }
    
    /// <summary>
    /// Staleness threshold that was applied
    /// </summary>
    public TimeSpan Threshold { get; set; }
    
    /// <summary>
    /// Type of data that was validated
    /// </summary>
    public DataType DataType { get; set; }
    
    /// <summary>
    /// Source of the data
    /// </summary>
    public string DataSource { get; set; } = string.Empty;
    
    /// <summary>
    /// Whether it was market hours during validation
    /// </summary>
    public bool IsMarketHours { get; set; }
    
    /// <summary>
    /// Whether the data should be rejected due to staleness
    /// </summary>
    public bool ShouldReject { get; set; }
    
    /// <summary>
    /// Timestamp when validation was performed
    /// </summary>
    public DateTime ValidationTimestamp { get; set; }
}

/// <summary>
/// Extension methods for data staleness validation
/// </summary>
public static class DataStalenessValidationExtensions
{
    /// <summary>
    /// Validates data freshness and throws exception if stale and rejection is enabled
    /// </summary>
    public static async Task<T> ValidateAndReturnAsync<T>(
        this IDataStalenessValidationService validator,
        T data,
        DateTime dataTimestamp,
        DataType dataType,
        string dataSource)
    {
        var result = await validator.ValidateDataFreshnessAsync(dataTimestamp, dataType, dataSource);
        
        if (result.ShouldReject)
        {
            throw new DataStalenessException(
                $"Data from {dataSource} is too stale: {result.DataAge} (threshold: {result.Threshold})",
                result);
        }
        
        return data;
    }
}

/// <summary>
/// Exception thrown when stale data is rejected
/// </summary>
public class DataStalenessException : Exception
{
    public DataStalenessValidationResult ValidationResult { get; }

    public DataStalenessException(string message, DataStalenessValidationResult validationResult) 
        : base(message)
    {
        ValidationResult = validationResult;
    }
}
