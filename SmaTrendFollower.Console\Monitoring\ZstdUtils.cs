using System.IO.Compression;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Monitoring;

/// <summary>
/// Utility class for Zstd compression with gzip fallback
/// </summary>
internal static class ZstdUtils
{
    private static readonly ILogger _logger = LoggerFactory.Create(builder => builder.AddConsole()).CreateLogger(typeof(ZstdUtils));

    /// <summary>
    /// Compresses a file using Zstd compression with gzip fallback
    /// </summary>
    /// <param name="src">Source file path</param>
    /// <param name="dstZst">Destination compressed file path</param>
    public static void CompressAndReplace(string src, string dstZst)
    {
        try
        {
            // Try Zstd compression first
            using var fin = File.OpenRead(src);
            using var fout = File.Create(dstZst);
            using var z = new ZstdNet.CompressionStream(fout, 3); // Level 3 for good balance of speed/compression
            fin.CopyTo(z);
            
            _logger.LogDebug("Successfully compressed {Source} to {Destination} using Zstd", src, dstZst);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Zstd compression failed for {Source}, falling back to gzip", src);
            
            // Fallback to gzip if Zstd not available
            try
            {
                using var fin = File.OpenRead(src);
                using var gz = new GZipStream(File.Create(dstZst + ".gz"), CompressionLevel.Optimal);
                fin.CopyTo(gz);
                
                _logger.LogDebug("Successfully compressed {Source} to {Destination} using gzip fallback", src, dstZst + ".gz");
            }
            catch (Exception gzipEx)
            {
                _logger.LogError(gzipEx, "Both Zstd and gzip compression failed for {Source}", src);
                throw;
            }
        }
    }
}
