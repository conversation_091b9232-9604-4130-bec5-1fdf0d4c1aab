using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Linq;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Integration;

/// <summary>
/// Simplified integration tests for core services that we know exist
/// </summary>
public class CoreServiceIntegrationTests : IDisposable
{
    private readonly IHost _host;
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public CoreServiceIntegrationTests()
    {
        // Create test configuration
        var configBuilder = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["Alpaca:KeyId"] = "test-key",
                ["Alpaca:SecretKey"] = "test-secret",
                ["Alpaca:Environment"] = "paper",
                ["AlpacaNews:KeyId"] = "test-alpaca-news-key-id",
                ["AlpacaNews:Secret"] = "test-alpaca-news-secret",
                ["Polygon:ApiKey"] = "test-polygon-key",
                ["Gemini:ApiKey"] = "test-gemini-key",
                ["Redis:ConnectionString"] = "*************:6379", // Use actual Redis server
                ["REDIS_CONNECTION_STRING"] = "*************:6379", // Environment variable fallback
                ["Redis__ConnectionString"] = "*************:6379", // Hierarchical environment variable
                ["WheelStrategy:Enabled"] = "true",
                ["WheelStrategy:MaxAllocationPercent"] = "0.10",
                ["Safety:DryRunMode"] = "true",
                ["ML:PositionModelPath"] = "SmaTrendFollower.Console/Model/position_model.zip",
                ["SlippageTraining:ModelOutputPath"] = "SmaTrendFollower.Console/Model/slippage_model.zip",

                // Enhanced Services Configuration - Disabled for testing to avoid DI issues
                ["EnhancedServices:EnableEnhancedDataRetrieval"] = "false",
                ["EnhancedServices:EnableAdaptiveRateLimit"] = "false",
                ["EnhancedServices:EnableAdaptiveSignalGeneration"] = "false",

                // Synthetic Data Configuration
                ["SyntheticData:RandomSeed"] = "",
                ["SyntheticData:DefaultStartPrice"] = "100.0",
                ["SyntheticData:VolumeMultiplier"] = "0.5",
                ["SyntheticData:MaxCorrelation"] = "0.95",
                ["SyntheticData:MinCorrelation"] = "0.1",
                ["SyntheticData:DefaultVolatility"] = "0.02",
                ["SyntheticData:UseSectorCorrelations"] = "true",

                // Enhanced Data Retrieval Configuration
                ["EnhancedDataRetrieval:MaxConcurrentRequests"] = "20",
                ["EnhancedDataRetrieval:PrimaryApiTimeout"] = "00:00:45",
                ["EnhancedDataRetrieval:BatchTimeout"] = "00:03:00",
                ["EnhancedDataRetrieval:RelaxedStalenessThreshold"] = "02:00:00",
                ["EnhancedDataRetrieval:EmergencyModeMaxStaleness"] = "1.00:00:00",
                ["EnhancedDataRetrieval:EmergencyModeTimeout"] = "00:15:00",
                ["EnhancedDataRetrieval:EnableSyntheticData"] = "true",
                ["EnhancedDataRetrieval:MinimumBatchSuccessRate"] = "0.7",
                ["EnhancedDataRetrieval:MaxFailedAttempts"] = "3",

                // Flexible Staleness Configuration
                ["FlexibleStaleness:DefaultStalenessThreshold"] = "00:18:00",
                ["FlexibleStaleness:AfterHoursStalenessThreshold"] = "08:00:00",
                ["FlexibleStaleness:EmergencyModeStalenessThreshold"] = "1.00:00:00",
                ["FlexibleStaleness:EnableFlexibleStaleness"] = "true",

                // Adaptive Rate Limiting Configuration
                ["AdaptiveRateLimit:AdjustmentInterval"] = "00:02:00",
                ["AdaptiveRateLimit:MinRequestsPerSecond"] = "1",
                ["AdaptiveRateLimit:MaxRequestsPerSecond"] = "100",
                ["AdaptiveRateLimit:SuccessRateThreshold"] = "0.95",
                ["AdaptiveRateLimit:ErrorRateThreshold"] = "0.05",
                ["AdaptiveRateLimit:BackoffMultiplier"] = "0.8",
                ["AdaptiveRateLimit:RecoveryMultiplier"] = "1.1",

                // Adaptive Signal Generation Configuration
                ["AdaptiveSignal:MaxSymbolsToProcess"] = "500",
                ["AdaptiveSignal:MinConfidenceScore"] = "0.5",
                ["AdaptiveSignal:EnableSyntheticData"] = "true",
                ["AdaptiveSignal:EnableFallbackStrategies"] = "true"
            });

        _configuration = configBuilder.Build();

        // Build host with proper hosting infrastructure for hosted services like Quartz
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(services =>
            {
                services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
                services.AddSingleton(_configuration);

                // Add mock Redis for testing
                services.AddSingleton<IConnectionMultiplexer>(provider =>
                {
                    // Use the mock Redis from test helpers
                    var mockMuxer = Substitute.For<IConnectionMultiplexer>();
                    var mockDb = TestHelpers.InMemoryRedis.Create();
                    mockMuxer.GetDatabase(Arg.Any<int>()).Returns(mockDb);
                    mockMuxer.IsConnected.Returns(true);
                    return mockMuxer;
                });

                // Add core services only to avoid circular dependency issues
                services.AddPollyHttpClients(_configuration);
                services.AddCoreInfrastructure();
                services.AddDataServices(_configuration);
                services.AddMarketDataServices();
                services.AddSafetyServices();
                services.AddTradingServices();

                // Use refactored trading services to avoid circular dependencies
                services.AddRefactoredTradingServices();
            })
            .Build();

        _serviceProvider = _host.Services;
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void CoreServices_CanBeResolved_WithoutExceptions()
    {
        // Arrange & Act & Assert - Core trading services that should always exist
        var marketDataService = _serviceProvider.GetService<IMarketDataService>();
        var signalGenerator = _serviceProvider.GetService<ISignalGenerator>();
        var riskManager = _serviceProvider.GetService<IRiskManager>();
        var tradeExecutor = _serviceProvider.GetService<ITradeExecutor>();
        var portfolioGate = _serviceProvider.GetService<IPortfolioGate>();
        var sessionGuard = _serviceProvider.GetService<IMarketSessionGuard>();

        // These core services should always be available
        marketDataService.Should().NotBeNull();
        signalGenerator.Should().NotBeNull();
        riskManager.Should().NotBeNull();
        tradeExecutor.Should().NotBeNull();
        portfolioGate.Should().NotBeNull();
        sessionGuard.Should().NotBeNull();
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void WheelStrategyService_CanBeResolved_WithoutExceptions()
    {
        // Arrange & Act & Assert - Wheel strategy service
        var wheelEngine = _serviceProvider.GetService<IWheelStrategyEngine>();
        
        wheelEngine.Should().NotBeNull();
        wheelEngine.Should().BeOfType<WheelStrategyEngine>();
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void MarketDataServices_CanBeResolved_WithoutExceptions()
    {
        // Arrange & Act & Assert - Market data related services
        var vixResolver = _serviceProvider.GetService<IVIXResolverService>();
        var universeProvider = _serviceProvider.GetService<IUniverseProvider>();
        var regimeService = _serviceProvider.GetService<IMarketRegimeService>();

        vixResolver.Should().NotBeNull();
        universeProvider.Should().NotBeNull();
        regimeService.Should().NotBeNull();
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void NotificationServices_CanBeResolved_WithoutExceptions()
    {
        // Arrange & Act & Assert - Notification services
        var discordService = _serviceProvider.GetService<IDiscordNotificationService>();
        
        discordService.Should().NotBeNull();
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void BackgroundServices_CanBeResolved_WithoutExceptions()
    {
        // Arrange & Act & Assert - Background services
        var hostedServices = _serviceProvider.GetServices<IHostedService>();

        // Filter out null services (services that are disabled when dependencies like Redis are not available)
        var nonNullHostedServices = hostedServices.Where(s => s != null).ToList();

        nonNullHostedServices.Should().NotBeEmpty();

        // Check for wheel strategy background service
        nonNullHostedServices.Should().Contain(s => s.GetType() == typeof(WheelStrategyEngine));
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void ConfigurationServices_CanBeResolved_WithoutExceptions()
    {
        // Arrange & Act & Assert - Configuration services
        var configuration = _serviceProvider.GetService<IConfiguration>();
        var logger = _serviceProvider.GetService<ILogger<CoreServiceIntegrationTests>>();
        
        configuration.Should().NotBeNull();
        logger.Should().NotBeNull();
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void ServiceResolution_NoCircularDependencies_DoesNotThrow()
    {
        // Arrange & Act
        var act = () =>
        {
            // Try to resolve all major services - this will fail if there are circular dependencies
            _serviceProvider.GetService<IMarketDataService>();
            _serviceProvider.GetService<IWheelStrategyEngine>();
            _serviceProvider.GetService<ISignalGenerator>();
            _serviceProvider.GetService<IRiskManager>();
            _serviceProvider.GetService<ITradeExecutor>();
            _serviceProvider.GetService<IPortfolioGate>();
            _serviceProvider.GetService<IMarketSessionGuard>();
            _serviceProvider.GetService<IVIXResolverService>();
            _serviceProvider.GetService<IUniverseProvider>();
            _serviceProvider.GetService<IMarketRegimeService>();
            _serviceProvider.GetService<IDiscordNotificationService>();
        };

        // Assert
        act.Should().NotThrow("there should be no circular dependencies in service registration");
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void ServiceProvider_CanCreateScope_WithoutExceptions()
    {
        // Arrange & Act
        using var scope = _serviceProvider.CreateScope();
        var scopedProvider = scope.ServiceProvider;

        // Assert
        scopedProvider.Should().NotBeNull();
        
        // Verify scoped services can be resolved
        var marketDataService = scopedProvider.GetService<IMarketDataService>();
        var wheelEngine = scopedProvider.GetService<IWheelStrategyEngine>();
        
        marketDataService.Should().NotBeNull();
        wheelEngine.Should().NotBeNull();
    }

    [Fact(Timeout = 10000)] // 10 second timeout to prevent hanging
    public void AllCoreServices_HaveCorrectLifetime_AndCanBeInstantiated()
    {
        // Arrange & Act
        var marketDataService1 = _serviceProvider.GetService<IMarketDataService>();
        var marketDataService2 = _serviceProvider.GetService<IMarketDataService>();
        
        var wheelEngine1 = _serviceProvider.GetService<IWheelStrategyEngine>();
        var wheelEngine2 = _serviceProvider.GetService<IWheelStrategyEngine>();

        // Assert - Services should be properly instantiated
        marketDataService1.Should().NotBeNull();
        marketDataService2.Should().NotBeNull();
        wheelEngine1.Should().NotBeNull();
        wheelEngine2.Should().NotBeNull();
        
        // For scoped services, same instance should be returned within same scope
        // For singleton services, same instance should always be returned
        // We don't test specific lifetime here, just that services are properly registered
    }

    public void Dispose()
    {
        _host?.Dispose();
    }
}
