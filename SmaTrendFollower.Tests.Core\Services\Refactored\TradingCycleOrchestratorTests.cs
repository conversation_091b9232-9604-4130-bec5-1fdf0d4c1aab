using Microsoft.Extensions.Logging;
using NSubstitute;
using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Services.Refactored;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests.Core.Services.Refactored;

public class TradingCycleOrchestratorTests
{
    private readonly IEquityTradingCycleService _equityTradingService;
    private readonly IOptionsOverlayService _optionsOverlayService;
    private readonly IPortfolioManagementService _portfolioManagementService;
    private readonly IRealTimeMonitoringService _realTimeMonitoringService;
    private readonly IVolatilityManager _volatilityManager;
    private readonly ITickVolatilityGuard _volatilityGuard;
    private readonly IDiscordNotificationService _discordService;
    private readonly ILogger<TradingCycleOrchestrator> _logger;
    private readonly TradingCycleOrchestrator _orchestrator;

    public TradingCycleOrchestratorTests()
    {
        _equityTradingService = Substitute.For<IEquityTradingCycleService>();
        _optionsOverlayService = Substitute.For<IOptionsOverlayService>();
        _portfolioManagementService = Substitute.For<IPortfolioManagementService>();
        _realTimeMonitoringService = Substitute.For<IRealTimeMonitoringService>();
        _volatilityManager = Substitute.For<IVolatilityManager>();
        _volatilityGuard = Substitute.For<ITickVolatilityGuard>();
        _discordService = Substitute.For<IDiscordNotificationService>();
        _logger = Substitute.For<ILogger<TradingCycleOrchestrator>>();

        _orchestrator = new TradingCycleOrchestrator(
            _equityTradingService,
            _optionsOverlayService,
            _portfolioManagementService,
            _realTimeMonitoringService,
            _volatilityManager,
            _volatilityGuard,
            _discordService,
            _logger);
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenAllServicesSucceed_CompletesSuccessfully()
    {
        // Arrange
        var vixRegime = new VolatilityRegime(20.5m, 18.0m, false, false, 1.0m, "Normal");
        _volatilityManager.GetCurrentRegimeAsync().Returns(vixRegime);
        _volatilityGuard.IsAnyTradingBlocked().Returns(false);

        var equityResult = new EquityTradingCycleResult
        {
            Success = true,
            TradesExecuted = 3,
            SignalsGenerated = 5,
            TotalTradeValue = 10000m
        };
        _equityTradingService.ExecuteEquityTradingCycleAsync(Arg.Any<VolatilityRegime>(), Arg.Any<CancellationToken>()).Returns(equityResult);

        var optionsResult = new OptionsOverlayResult
        {
            Success = true,
            CoveredCallsEvaluated = 2,
            ProtectivePutsEvaluated = 1
        };
        _optionsOverlayService.ExecuteOptionsOverlayAsync(Arg.Any<CancellationToken>()).Returns(optionsResult);

        var portfolioResult = new PortfolioManagementResult
        {
            Success = true,
            PositionsMonitored = 5,
            SnapshotSent = true
        };
        _portfolioManagementService.ExecutePortfolioManagementAsync(Arg.Any<CancellationToken>()).Returns(portfolioResult);

        var monitoringResult = new RealTimeMonitoringResult
        {
            Success = true,
            SymbolsMonitored = 3,
            ServicesStarted = 7
        };
        _realTimeMonitoringService.StartMonitoringAsync(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>())
            .Returns(monitoringResult);

        // Act
        await _orchestrator.ExecuteCycleAsync();

        // Assert
        _orchestrator.Status.Should().Be(TradingCycleOrchestratorStatus.Completed);
        _orchestrator.LastResult.Should().NotBeNull();
        _orchestrator.LastResult!.Success.Should().BeTrue();
        _orchestrator.LastResult.EquityResult.Should().Be(equityResult);
        _orchestrator.LastResult.OptionsResult.Should().Be(optionsResult);
        _orchestrator.LastResult.PortfolioResult.Should().Be(portfolioResult);
        _orchestrator.LastResult.MonitoringResult.Should().Be(monitoringResult);
        _orchestrator.LastResult.Errors.Should().BeEmpty();
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenEquityTradingFails_ContinuesWithOtherServices()
    {
        // Arrange
        var vixRegime = new VolatilityRegime(20.5m, 18.0m, false, false, 1.0m, "Normal");
        _volatilityManager.GetCurrentRegimeAsync().Returns(vixRegime);
        _volatilityGuard.IsAnyTradingBlocked().Returns(false);

        var equityResult = new EquityTradingCycleResult
        {
            Success = false,
            Message = "Equity trading failed",
            Errors = new List<string> { "Signal generation error" }
        };
        _equityTradingService.ExecuteEquityTradingCycleAsync(Arg.Any<VolatilityRegime>(), Arg.Any<CancellationToken>()).Returns(equityResult);

        var optionsResult = new OptionsOverlayResult { Success = true };
        _optionsOverlayService.ExecuteOptionsOverlayAsync(Arg.Any<CancellationToken>()).Returns(optionsResult);

        var portfolioResult = new PortfolioManagementResult { Success = true };
        _portfolioManagementService.ExecutePortfolioManagementAsync(Arg.Any<CancellationToken>()).Returns(portfolioResult);

        // Act
        await _orchestrator.ExecuteCycleAsync();

        // Assert
        _orchestrator.Status.Should().Be(TradingCycleOrchestratorStatus.Completed);
        _orchestrator.LastResult.Should().NotBeNull();
        _orchestrator.LastResult!.Success.Should().BeFalse(); // Overall failure due to equity trading
        _orchestrator.LastResult.Errors.Should().Contain("Signal generation error");
        
        // Should still execute other services
        await _optionsOverlayService.Received(1).ExecuteOptionsOverlayAsync(Arg.Any<CancellationToken>());
        await _portfolioManagementService.Received(1).ExecutePortfolioManagementAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenNoTradesExecuted_SkipsRealTimeMonitoring()
    {
        // Arrange
        var vixRegime = new VolatilityRegime(20.5m, 18.0m, false, false, 1.0m, "Normal");
        _volatilityManager.GetCurrentRegimeAsync().Returns(vixRegime);
        _volatilityGuard.IsAnyTradingBlocked().Returns(false);

        var equityResult = new EquityTradingCycleResult
        {
            Success = true,
            TradesExecuted = 0 // No trades executed
        };
        _equityTradingService.ExecuteEquityTradingCycleAsync(Arg.Any<VolatilityRegime>(), Arg.Any<CancellationToken>()).Returns(equityResult);

        var optionsResult = new OptionsOverlayResult { Success = true };
        _optionsOverlayService.ExecuteOptionsOverlayAsync(Arg.Any<CancellationToken>()).Returns(optionsResult);

        var portfolioResult = new PortfolioManagementResult { Success = true };
        _portfolioManagementService.ExecutePortfolioManagementAsync(Arg.Any<CancellationToken>()).Returns(portfolioResult);

        // Act
        await _orchestrator.ExecuteCycleAsync();

        // Assert
        _orchestrator.Status.Should().Be(TradingCycleOrchestratorStatus.Completed);
        _orchestrator.LastResult!.MonitoringResult.Should().BeNull();
        
        // Should not start real-time monitoring
        await _realTimeMonitoringService.DidNotReceive().StartMonitoringAsync(Arg.Any<IEnumerable<string>>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenCancelled_StopsExecution()
    {
        // Arrange
        using var cts = new CancellationTokenSource();
        cts.Cancel();

        // Act
        await _orchestrator.ExecuteCycleAsync(cts.Token);

        // Assert
        // Should not execute any services when cancelled immediately
        await _equityTradingService.DidNotReceive().ExecuteEquityTradingCycleAsync(Arg.Any<VolatilityRegime>(), Arg.Any<CancellationToken>());
    }

    [Fact]
    public async Task ExecuteCycleAsync_WhenServiceThrowsException_HandlesGracefully()
    {
        // Arrange
        var vixRegime = new VolatilityRegime(20.5m, 18.0m, false, false, 1.0m, "Normal");
        _volatilityManager.GetCurrentRegimeAsync().Returns(vixRegime);
        _volatilityGuard.IsAnyTradingBlocked().Returns(false);

        _equityTradingService.ExecuteEquityTradingCycleAsync(Arg.Any<VolatilityRegime>(), Arg.Any<CancellationToken>())
            .Returns(Task.FromException<EquityTradingCycleResult>(new InvalidOperationException("Service crashed")));

        var optionsResult = new OptionsOverlayResult { Success = true };
        _optionsOverlayService.ExecuteOptionsOverlayAsync(Arg.Any<CancellationToken>()).Returns(optionsResult);

        var portfolioResult = new PortfolioManagementResult { Success = true };
        _portfolioManagementService.ExecutePortfolioManagementAsync(Arg.Any<CancellationToken>()).Returns(portfolioResult);

        // Act
        await _orchestrator.ExecuteCycleAsync();

        // Assert
        _orchestrator.Status.Should().Be(TradingCycleOrchestratorStatus.Completed);
        _orchestrator.LastResult.Should().NotBeNull();
        _orchestrator.LastResult!.Success.Should().BeFalse();
        _orchestrator.LastResult.Errors.Should().Contain(e => e.Contains("Service crashed"));
        
        // Should still continue with other services
        await _optionsOverlayService.Received(1).ExecuteOptionsOverlayAsync(Arg.Any<CancellationToken>());
        await _portfolioManagementService.Received(1).ExecutePortfolioManagementAsync(Arg.Any<CancellationToken>());
    }

    [Fact]
    public void Constructor_WithNullDependencies_ThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new TradingCycleOrchestrator(
            null!, _optionsOverlayService, _portfolioManagementService, _realTimeMonitoringService,
            _volatilityManager, _volatilityGuard, _discordService, _logger));

        Assert.Throws<ArgumentNullException>(() => new TradingCycleOrchestrator(
            _equityTradingService, null!, _portfolioManagementService, _realTimeMonitoringService,
            _volatilityManager, _volatilityGuard, _discordService, _logger));

        Assert.Throws<ArgumentNullException>(() => new TradingCycleOrchestrator(
            _equityTradingService, _optionsOverlayService, null!, _realTimeMonitoringService,
            _volatilityManager, _volatilityGuard, _discordService, _logger));

        Assert.Throws<ArgumentNullException>(() => new TradingCycleOrchestrator(
            _equityTradingService, _optionsOverlayService, _portfolioManagementService, null!,
            _volatilityManager, _volatilityGuard, _discordService, _logger));
    }

    [Fact]
    public void InitialState_ShouldBeCorrect()
    {
        // Assert
        _orchestrator.Status.Should().Be(TradingCycleOrchestratorStatus.Idle);
        _orchestrator.LastResult.Should().BeNull();
    }
}
