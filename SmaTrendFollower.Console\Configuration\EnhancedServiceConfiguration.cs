using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Service registration extensions for enhanced data retrieval and signal generation components
/// </summary>
public static class EnhancedServiceConfiguration
{
    /// <summary>
    /// Adds enhanced data retrieval services with multi-tier fallback strategies
    /// </summary>
    public static IServiceCollection AddEnhancedDataRetrievalServices(
        this IServiceCollection services, 
        IConfiguration? configuration = null)
    {
        // Configuration
        services.Configure<DataRetrievalConfiguration>(configuration?.GetSection("EnhancedDataRetrieval") ?? 
            new ConfigurationBuilder().Build().GetSection("EnhancedDataRetrieval"));
        
        services.Configure<SyntheticDataConfiguration>(configuration?.GetSection("SyntheticData") ?? 
            new ConfigurationBuilder().Build().GetSection("SyntheticData"));

        services.Configure<FlexibleStalenessConfiguration>(configuration?.GetSection("FlexibleStaleness") ?? 
            new ConfigurationBuilder().Build().GetSection("FlexibleStaleness"));

        // Core enhanced services
        services.AddSingleton<ISyntheticDataGenerator, SyntheticDataGenerator>();
        services.AddSingleton<IFlexibleDataStalenessService, FlexibleDataStalenessService>();
        services.AddSingleton<IEnhancedDataRetrievalService, EnhancedDataRetrievalService>();

        // Enhanced market data service (decorator pattern)
        services.Decorate<IMarketDataService, EnhancedMarketDataServiceDecorator>();

        return services;
    }

    /// <summary>
    /// Adds adaptive rate limiting and intelligent batching services
    /// </summary>
    public static IServiceCollection AddAdaptiveRateLimitingServices(
        this IServiceCollection services,
        IConfiguration? configuration = null)
    {
        // Configuration
        services.Configure<AdaptiveRateLimitConfiguration>(configuration?.GetSection("AdaptiveRateLimit") ?? 
            new ConfigurationBuilder().Build().GetSection("AdaptiveRateLimit"));

        // Rate limiting services
        services.AddSingleton<IAdaptiveRateLimitingService, AdaptiveRateLimitingService>();

        return services;
    }

    /// <summary>
    /// Adds adaptive signal generation services with multiple strategies
    /// </summary>
    public static IServiceCollection AddAdaptiveSignalGenerationServices(
        this IServiceCollection services,
        IConfiguration? configuration = null)
    {
        // Configuration
        services.Configure<AdaptiveSignalConfiguration>(configuration?.GetSection("AdaptiveSignal") ?? 
            new ConfigurationBuilder().Build().GetSection("AdaptiveSignal"));

        services.Configure<RobustSignalConfiguration>(configuration?.GetSection("RobustSignal") ?? 
            new ConfigurationBuilder().Build().GetSection("RobustSignal"));

        // Signal generation services
        services.AddScoped<IAdaptiveSignalGenerator, AdaptiveSignalGenerator>();
        services.AddScoped<IRobustSignalGenerationService, RobustSignalGenerationService>();

        // Enhanced signal generator (decorator pattern)
        services.Decorate<ISignalGenerator, EnhancedSignalGeneratorDecorator>();

        return services;
    }

    /// <summary>
    /// Adds all enhanced services with feature flag support
    /// </summary>
    public static IServiceCollection AddEnhancedTradingSystemServices(
        this IServiceCollection services,
        IConfiguration configuration,
        EnhancedServicesOptions? options = null)
    {
        options ??= new EnhancedServicesOptions();

        // Bind options from configuration
        configuration.GetSection("EnhancedServices").Bind(options);
        services.AddSingleton(options);

        // Add enhanced services based on feature flags
        if (options.EnableEnhancedDataRetrieval)
        {
            services.AddEnhancedDataRetrievalServices(configuration);
        }

        if (options.EnableAdaptiveRateLimit)
        {
            services.AddAdaptiveRateLimitingServices(configuration);
        }

        if (options.EnableAdaptiveSignalGeneration)
        {
            services.AddAdaptiveSignalGenerationServices(configuration);
        }

        // Add market hours service if not already registered
        services.TryAddSingleton<IMarketHoursService, MarketHoursService>();

        return services;
    }

    /// <summary>
    /// Adds enhanced services for testing with mocked dependencies
    /// </summary>
    public static IServiceCollection AddEnhancedServicesForTesting(
        this IServiceCollection services,
        IConfiguration? configuration = null)
    {
        var testConfig = configuration ?? new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["EnhancedServices:EnableEnhancedDataRetrieval"] = "true",
                ["EnhancedServices:EnableAdaptiveRateLimit"] = "true",
                ["EnhancedServices:EnableAdaptiveSignalGeneration"] = "true",
                ["EnhancedDataRetrieval:MaxConcurrentRequests"] = "10",
                ["EnhancedDataRetrieval:PrimaryApiTimeout"] = "00:00:30",
                ["AdaptiveRateLimit:AdjustmentInterval"] = "00:02:00",
                ["AdaptiveSignal:MaxSymbolsToProcess"] = "100"
            })
            .Build();

        return services.AddEnhancedTradingSystemServices(testConfig);
    }
}

/// <summary>
/// Options for controlling which enhanced services are enabled
/// </summary>
public sealed class EnhancedServicesOptions
{
    /// <summary>
    /// Enable enhanced data retrieval with multi-tier fallbacks
    /// </summary>
    public bool EnableEnhancedDataRetrieval { get; set; } = true;

    /// <summary>
    /// Enable adaptive rate limiting and intelligent batching
    /// </summary>
    public bool EnableAdaptiveRateLimit { get; set; } = true;

    /// <summary>
    /// Enable adaptive signal generation with multiple strategies
    /// </summary>
    public bool EnableAdaptiveSignalGeneration { get; set; } = true;

    /// <summary>
    /// Enable comprehensive metrics and monitoring
    /// </summary>
    public bool EnableEnhancedMetrics { get; set; } = true;

    /// <summary>
    /// Enable synthetic data generation for fallback scenarios
    /// </summary>
    public bool EnableSyntheticData { get; set; } = true;

    /// <summary>
    /// Enable emergency mode capabilities
    /// </summary>
    public bool EnableEmergencyMode { get; set; } = true;
}

/// <summary>
/// Extension methods for service decoration
/// </summary>
public static class ServiceDecorationExtensions
{
    /// <summary>
    /// Decorates a service with an enhanced implementation
    /// </summary>
    public static IServiceCollection Decorate<TInterface, TDecorator>(
        this IServiceCollection services)
        where TInterface : class
        where TDecorator : class, TInterface
    {
        // Find the existing service registration
        var existingService = services.FirstOrDefault(s => s.ServiceType == typeof(TInterface));
        if (existingService == null)
        {
            throw new InvalidOperationException($"Service {typeof(TInterface).Name} is not registered");
        }

        // Remove the existing registration
        services.Remove(existingService);

        // Register the decorator
        services.Add(new ServiceDescriptor(
            typeof(TInterface),
            provider =>
            {
                // Create the original service
                var originalService = existingService.ImplementationFactory?.Invoke(provider) ??
                    (existingService.ImplementationType != null 
                        ? ActivatorUtilities.CreateInstance(provider, existingService.ImplementationType)
                        : existingService.ImplementationInstance) ??
                    throw new InvalidOperationException($"Could not create original service {typeof(TInterface).Name}");

                // Create the decorator with the original service as dependency
                return ActivatorUtilities.CreateInstance(provider, typeof(TDecorator), originalService);
            },
            existingService.Lifetime));

        return services;
    }

    /// <summary>
    /// Tries to add a service if it's not already registered
    /// </summary>
    public static IServiceCollection TryAddSingleton<TInterface, TImplementation>(
        this IServiceCollection services)
        where TInterface : class
        where TImplementation : class, TInterface
    {
        if (!services.Any(s => s.ServiceType == typeof(TInterface)))
        {
            services.AddSingleton<TInterface, TImplementation>();
        }
        return services;
    }
}

/// <summary>
/// Simple market hours service implementation
/// </summary>
public sealed class MarketHoursService : IMarketHoursService
{
    public bool IsMarketHours()
    {
        var now = DateTime.Now;
        var easternTime = TimeZoneInfo.ConvertTime(now, TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time"));
        
        // Market is open Monday-Friday 9:30 AM - 4:00 PM ET
        if (easternTime.DayOfWeek == DayOfWeek.Saturday || easternTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);
        
        return easternTime.TimeOfDay >= marketOpen && easternTime.TimeOfDay <= marketClose;
    }
}

/// <summary>
/// Interface for market hours service
/// </summary>
public interface IMarketHoursService
{
    bool IsMarketHours();
}
