using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net.Http.Json;
using System.Text.Json.Nodes;
using System.Threading.Channels;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Background service that polls Brave Search News API for headlines and feeds them into the FinBERT sentiment pipeline.
/// Implements deduplication, rate limiting, and parallel symbol querying to complement Alpaca news feeds.
/// </summary>
internal sealed class BraveNewsPoller : BackgroundService
{
    private readonly BraveOptions _opt;
    private readonly IHttpClientFactory _http;
    private readonly ChannelWriter<HeadlineItem> _writer;
    private readonly ILogger<BraveNewsPoller> _log;
    private readonly HashSet<string> _seen = new();     // Dedupe headlines by ID

    /// <summary>
    /// Initializes the Brave news poller with configuration and dependencies.
    /// </summary>
    /// <param name="opt">Brave Search API configuration options</param>
    /// <param name="http">HTTP client factory for API calls</param>
    /// <param name="chan">Channel for writing headlines to FinBERT worker</param>
    /// <param name="log">Logger for service operations</param>
    public BraveNewsPoller(IOptions<BraveOptions> opt,
                           IHttpClientFactory http,
                           Channel<HeadlineItem> chan,
                           ILogger<BraveNewsPoller> log)
    {
        _opt = opt.Value ?? throw new ArgumentNullException(nameof(opt));
        _http = http ?? throw new ArgumentNullException(nameof(http));
        _writer = chan.Writer ?? throw new ArgumentNullException(nameof(chan));
        _log = log ?? throw new ArgumentNullException(nameof(log));
    }

    /// <summary>
    /// Continuously polls Brave Search API for news headlines across configured symbols.
    /// Respects rate limits and implements configurable polling intervals.
    /// </summary>
    protected override async Task ExecuteAsync(CancellationToken ct)
    {
        if (_opt.Symbols.Length == 0) 
        { 
            _log.LogWarning("Brave symbols array is empty; poller will remain idle."); 
            return; 
        }

        var delay = TimeSpan.FromMinutes(_opt.LookbackMins);
        _log.LogInformation("BraveNewsPoller started with {SymbolCount} symbols, {DelayMins}min intervals", 
            _opt.Symbols.Length, _opt.LookbackMins);

        while (!ct.IsCancellationRequested)
        {
            try
            {
                foreach (var sym in _opt.Symbols)
                {
                    await QuerySymbolAsync(sym, ct);
                    
                    // Rate limiting: 1 req/sec for free tier
                    await Task.Delay(1000, ct);
                }
            }
            catch (OperationCanceledException) when (ct.IsCancellationRequested)
            {
                _log.LogInformation("BraveNewsPoller cancellation requested");
                throw;
            }
            catch (Exception ex) 
            { 
                _log.LogError(ex, "Brave news polling cycle failed"); 
            }

            await Task.Delay(delay, ct);
        }
    }

    /// <summary>
    /// Queries Brave Search API for news headlines related to a specific symbol.
    /// Parses JSON response and writes new headlines to the sentiment processing channel.
    /// </summary>
    /// <param name="sym">Stock symbol to query for news</param>
    /// <param name="ct">Cancellation token</param>
    private async Task QuerySymbolAsync(string sym, CancellationToken ct)
    {
        try
        {
            var url = $"{_opt.BaseUrl}?q={Uri.EscapeDataString(sym)}&page_size={_opt.PageSize}";
            var cli = _http.CreateClient("BraveNews");
            
            var json = await cli.GetFromJsonAsync<JsonObject>(url, ct);
            if (json?["results"] is not JsonArray results) 
            {
                _log.LogDebug("No results found for symbol {Symbol}", sym);
                return;
            }

            var newHeadlines = 0;
            foreach (var r in results.OfType<JsonObject>())
            {
                var id = (string?)r["id"] ?? Guid.NewGuid().ToString("N");
                if (!_seen.Add(id)) continue; // Skip duplicates

                var headline = (string?)r["title"] ?? "";
                if (string.IsNullOrWhiteSpace(headline)) continue;

                var tsStr = (string?)r["publishedAt"] ?? "";
                if (!DateTime.TryParse(tsStr, out var tsUtc)) 
                    tsUtc = DateTime.UtcNow;

                var item = new HeadlineItem(id, sym.ToUpperInvariant(), headline, tsUtc);
                await _writer.WriteAsync(item, ct);
                newHeadlines++;
            }
            
            if (newHeadlines > 0)
            {
                _log.LogDebug("Brave queued {Count} new headlines for {Symbol}", newHeadlines, sym);
            }
        }
        catch (HttpRequestException ex)
        {
            _log.LogWarning(ex, "Brave API HTTP error for symbol {Symbol}", sym);
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
        {
            _log.LogWarning("Brave API timeout for symbol {Symbol}", sym);
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Unexpected error querying Brave API for symbol {Symbol}", sym);
        }
    }
}
