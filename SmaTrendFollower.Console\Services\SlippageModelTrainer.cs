using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IO.Compression;
using System.Text.Json;
using MathNet.Numerics.LinearAlgebra;
using MathNet.Numerics.LinearAlgebra.Double;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Offline job for training slippage prediction model using ordinary least squares regression.
/// Schedule daily after market close to retrain with fresh fill data.
/// </summary>
public sealed class SlippageModelTrainer
{
    private readonly SlippageOptions _opt;
    private readonly ILogger<SlippageModelTrainer> _log;

    public SlippageModelTrainer(IOptions<SlippageOptions> opt, ILogger<SlippageModelTrainer> log)
    {
        _opt = opt.Value;
        _log = log;
    }

    /// <summary>
    /// Train slippage model using ordinary least squares regression
    /// </summary>
    /// <param name="samples">Training samples with (spread, volPct, actualSlippage) tuples</param>
    /// <returns>True if training succeeded and model was saved</returns>
    public bool Train(IEnumerable<(double spread, double volPct, double slip)> samples)
    {
        var sampleList = samples.ToList();

        if (sampleList.Count < 200)
        {
            _log.LogWarning("Too few samples for training: {SampleCount} (minimum: 200)", sampleList.Count);
            return false;
        }

        try
        {
            _log.LogInformation("Training slippage model with {SampleCount} samples", sampleList.Count);

            // Prepare feature matrix X and target vector y
            // Features: [1, spread, volPct] (intercept, spread coefficient, volatility coefficient)
            var X = CreateFeatureMatrix(sampleList);
            var y = CreateTargetVector(sampleList);

            // Solve normal equation: w = (X^T * X)^(-1) * X^T * y
            var weights = SolveNormalEquation(X, y);

            // Validate weights are reasonable
            if (!ValidateWeights(weights))
            {
                _log.LogWarning("Trained weights failed validation, not saving model");
                return false;
            }

            // Save the trained model
            SaveWeights(weights);

            _log.LogInformation("Slippage model trained successfully: Intercept={Intercept:F3}¢, " +
                              "SpreadCoeff={SpreadCoeff:F3}, VolCoeff={VolCoeff:F3}",
                weights[0], weights[1], weights[2]);

            return true;
        }
        catch (Exception ex)
        {
            _log.LogError(ex, "Error during slippage model training");
            return false;
        }
    }

    private static Matrix<double> CreateFeatureMatrix(List<(double spread, double volPct, double slip)> samples)
    {
        var n = samples.Count;
        var X = DenseMatrix.Create(n, 3, 0.0);

        for (int i = 0; i < n; i++)
        {
            X[i, 0] = 1.0;                           // Intercept
            X[i, 1] = samples[i].spread * 100;       // Spread in cents
            X[i, 2] = samples[i].volPct;             // Volatility percentage
        }

        return X;
    }

    private static Vector<double> CreateTargetVector(List<(double spread, double volPct, double slip)> samples)
    {
        var y = DenseVector.Create(samples.Count, 0.0);

        for (int i = 0; i < samples.Count; i++)
        {
            y[i] = samples[i].slip; // Actual slippage in cents
        }

        return y;
    }

    private Vector<double> SolveNormalEquation(Matrix<double> X, Vector<double> y)
    {
        try
        {
            // Use QR decomposition for numerical stability
            var qr = X.QR();
            var weights = qr.Solve(y);

            _log.LogDebug("Normal equation solved using QR decomposition");
            return weights;
        }
        catch (Exception ex)
        {
            _log.LogWarning(ex, "QR decomposition failed, falling back to pseudo-inverse");

            // Fallback to pseudo-inverse using SVD
            var xt = X.Transpose();
            var weights = (xt * X).Inverse() * xt * y;

            return weights;
        }
    }

    private static bool ValidateWeights(Vector<double> weights)
    {
        // Basic sanity checks for the trained weights
        if (weights.Count != 3) return false;

        // Intercept should be reasonable (0.1¢ to 5¢)
        if (weights[0] < 0.1 || weights[0] > 5.0) return false;

        // Spread coefficient should be positive and reasonable (0 to 2)
        if (weights[1] < 0 || weights[1] > 2.0) return false;

        // Volatility coefficient should be reasonable (-1 to 1)
        if (weights[2] < -1.0 || weights[2] > 1.0) return false;

        return true;
    }

    private void SaveWeights(Vector<double> weights)
    {
        var weightsArray = weights.ToArray();

        // Ensure directory exists
        var directory = Path.GetDirectoryName(_opt.ModelPath);
        if (!string.IsNullOrEmpty(directory))
        {
            Directory.CreateDirectory(directory);
        }

        // Save weights as JSON in a zip file
        using var fs = File.Create(_opt.ModelPath);
        using var zip = new ZipArchive(fs, ZipArchiveMode.Create);
        var entry = zip.CreateEntry("weights.json");

        using var sw = new StreamWriter(entry.Open());
        var json = JsonSerializer.Serialize(weightsArray, new JsonSerializerOptions
        {
            WriteIndented = true
        });
        sw.Write(json);

        _log.LogInformation("Slippage model saved to {ModelPath}", _opt.ModelPath);
    }
}
