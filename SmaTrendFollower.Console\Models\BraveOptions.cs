namespace SmaTrendFollower.Models;

/// <summary>
/// Configuration options for Brave Search News API integration.
/// Controls API access, query parameters, and symbol filtering for news sentiment analysis.
/// </summary>
public sealed record BraveOptions
{
    /// <summary>
    /// Brave Search API key for Data for AI plan (free tier)
    /// </summary>
    public string Api<PERSON>ey { get; init; } = "";
    
    /// <summary>
    /// Number of news results to fetch per query (default: 20, max: 20 for free tier)
    /// </summary>
    public int PageSize { get; init; } = 20;
    
    /// <summary>
    /// Lookback window in minutes for polling fresh news (default: 15 minutes)
    /// </summary>
    public int LookbackMins { get; init; } = 15;
    
    /// <summary>
    /// Base URL for Brave Search News API endpoint
    /// </summary>
    public string BaseUrl { get; init; } = "https://api.search.brave.com/res/v1/news/search";
    
    /// <summary>
    /// Universe symbols to query for news (case-insensitive).
    /// Keep ≤ 50 symbols to respect free tier rate limits (1 req/sec, 2000 req/month).
    /// </summary>
    public string[] Symbols { get; init; } = Array.Empty<string>();
}
